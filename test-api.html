<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>API Test</h1>
    <button onclick="testAPI()">Test Jobs API</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('http://localhost:3001/api/jobs');
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <h2>Success!</h2>
                    <p>Found ${data.jobs.length} jobs</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <h2>Error!</h2>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
