import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Plus,
  Eye,
  Users,
  FileText,
  BarChart3,
  Filter,
  Search,
  Download,
  Clock,
  AlertCircle,
  Grid,
  List,
  CheckCircle,
  MoreHorizontal,
  TrendingUp,
  Activity,
  Edit,
  Share2,
  PieChart,
  RefreshCw,
  ChevronDown,
  Trash2,
  ExternalLink
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { shadows } from '@/components/ui/shadow';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import PageHeader from '@/components/layout/PageHeader';
import { applicationApi, formApi, analyticsApi } from '@/services/api';
import { useIsMobile } from '@/hooks/useIsMobile';

// Types
interface ApplicationFormSchema {
  id: string;
  title: string;
  description?: string;
  fields: any[];
  status: 'draft' | 'live' | 'archived';
  submissionCount?: number;
  viewCount?: number;
  createdAt: string;
  updatedAt: string;
}

interface Application {
  id: string;
  candidateName: string;
  jobTitle: string;
  status: string;
  score?: number;
  submittedAt: string;
  formId: string;
}

const Applications = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const isMobile = useIsMobile();

  // Enhanced state management for improved UX
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedTimeframe, setSelectedTimeframe] = useState('30d');
  const [applicationStats, setApplicationStats] = useState<any>(null);
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('table');
  const [forms, setForms] = useState<any[]>([]);
  const [applications, setApplications] = useState<any[]>([]);
  const [analyticsData, setAnalyticsData] = useState<any>(null);

  // Loading states
  const [loading, setLoading] = useState(true);
  const [formsLoading, setFormsLoading] = useState(false);
  const [analyticsLoading, setAnalyticsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Enhanced filtering and search state
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [positionFilter, setPositionFilter] = useState('all');
  const [selectedApplications, setSelectedApplications] = useState<string[]>([]);
  const [sortField, setSortField] = useState('submittedAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Get the first available job or use default
  const defaultJob = {
    id: 'default',
    title: 'General Application',
    department: 'Various',
    location: 'Remote',
    type: 'Full-time'
  };

  // Utility functions for enhanced UX
  const getStatusColor = (status: string) => {
    const colors = {
      'applied': 'bg-blue-100 text-blue-800',
      'review': 'bg-yellow-100 text-yellow-800',
      'screening': 'bg-purple-100 text-purple-800',
      'interview': 'bg-indigo-100 text-indigo-800',
      'offer': 'bg-green-100 text-green-800',
      'hired': 'bg-emerald-100 text-emerald-800',
      'rejected': 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getFormStatusColor = (status: string) => {
    return status === 'live' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Load data based on active tab
  const loadData = useCallback(async (tabName: string) => {
    try {
      setLoading(true);

      switch (tabName) {
        case 'dashboard':
          const overviewResponse = await applicationApi.getOverview({ timeframe: selectedTimeframe });
          setApplicationStats(overviewResponse);
          break;

        case 'applications':
          const applicationsResponse = await applicationApi.getApplications({
            limit: 50,
            status: statusFilter !== 'all' ? statusFilter : undefined,
            search: searchQuery || undefined
          });
          setApplications(applicationsResponse.applications || []);
          break;

        case 'forms':
          setFormsLoading(true);
          const formsResponse = await formApi.getForms({ limit: 50 });
          setForms(formsResponse.forms || []);
          setFormsLoading(false);
          break;

        case 'performance':
          setAnalyticsLoading(true);
          const analyticsResponse = await analyticsApi.getPerformanceAnalytics({ timeframe: selectedTimeframe });
          setAnalyticsData(analyticsResponse);
          setAnalyticsLoading(false);
          break;
      }
    } catch (error) {
      console.error(`Failed to load ${tabName} data:`, error);
      toast({
        title: 'Loading Error',
        description: `Failed to load ${tabName} data. Please try again.`,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [selectedTimeframe, statusFilter, searchQuery, toast]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await loadData(activeTab);
      toast({
        title: 'Data Refreshed',
        description: 'All data has been updated successfully.',
        variant: 'default',
      });
    } catch (error) {
      console.error('Failed to refresh data:', error);
      toast({
        title: 'Refresh Failed',
        description: 'Failed to refresh data. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setRefreshing(false);
    }
  }, [activeTab, loadData, toast]);

  // Load initial data and reload when tab changes
  useEffect(() => {
    loadData(activeTab);
  }, [activeTab, loadData]);

  // Reload data when timeframe changes
  useEffect(() => {
    if (activeTab === 'dashboard' || activeTab === 'performance') {
      loadData(activeTab);
    }
  }, [selectedTimeframe, activeTab, loadData]);

  // Event handlers
  const handlePreviewForm = (form: ApplicationFormSchema) => {
    setFormSchema(form);
    setShowPublicForm(true);
  };

  const handleCreateForm = () => {
    setFormSchema(null);
    setShowFormBuilder(true);
  };

  const handleEditForm = (form: ApplicationFormSchema) => {
    setFormSchema(form);
    setShowFormBuilder(true);
  };

  if (loading && !applicationStats && !applications.length && !forms.length) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 -m-6 p-6 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 -m-6 p-6">
      <div className="space-y-6">
        {/* Standardized Page Header */}
        <PageHeader
          title="Application Management"
          subtitle="Monitor applications, manage forms, and track performance"
          icon={FileText}
        >
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
            className="border-gray-300 hover:border-blue-500 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={async () => {
              try {
                const exportData = await applicationApi.exportApplications();
                toast({
                  title: 'Export Started',
                  description: `Exporting ${exportData.count} applications...`,
                });
              } catch (error) {
                toast({
                  title: 'Export Failed',
                  description: 'Failed to export data. Please try again.',
                  variant: 'destructive',
                });
              }
            }}
            className="border-gray-300 hover:border-blue-500 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200"
          >
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
          <Button
            size="sm"
            onClick={() => navigate('/jobs')}
            className="bg-blue-600 hover:bg-blue-700 text-white shadow-sm transition-all duration-200"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Job
          </Button>
        </PageHeader>

        {/* Enhanced Navigation Tabs - Mobile Responsive */}
        <div className={`${shadows.card} p-1`}>
          <div className="flex space-x-1 overflow-x-auto">
            {[
              { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
              { id: 'applications', label: 'Applications', icon: FileText },
              { id: 'forms', label: 'Forms', icon: Edit },
              { id: 'performance', label: 'Performance', icon: TrendingUp }
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 px-4 py-3 rounded-lg font-medium text-sm transition-all duration-200 whitespace-nowrap ${
                    activeTab === tab.id
                      ? 'bg-blue-600 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  {!isMobile && <span>{tab.label}</span>}
                </button>
              );
            })}
          </div>
        </div>
        {/* Dashboard Tab */}
        {activeTab === 'dashboard' && (
          <div className="space-y-6">
            {/* Key Metrics - Mobile Responsive Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className={`${shadows.card} p-6`}>
                <div className="flex items-center">
                  <div className="p-3 bg-blue-100 rounded-lg">
                    <FileText className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Applications</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {loading ? '...' : (applicationStats?.totalApplications || 0)}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {selectedTimeframe === '7d' ? 'Last 7 days' :
                       selectedTimeframe === '30d' ? 'Last 30 days' :
                       selectedTimeframe === '90d' ? 'Last 90 days' : 'Last year'}
                    </p>
                  </div>
                </div>
              </div>

              <div className={`${shadows.card} p-6`}>
                <div className="flex items-center">
                  <div className="p-3 bg-green-100 rounded-lg">
                    <TrendingUp className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">New Applications</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {loading ? '...' : (applicationStats?.newApplications || 0)}
                    </p>
                    <p className={`text-xs mt-1 ${
                      (applicationStats?.growthRate || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {(applicationStats?.growthRate || 0) >= 0 ? '+' : ''}{applicationStats?.growthRate || 0}% from last period
                    </p>
                  </div>
                </div>
              </div>

              <div className={`${shadows.card} p-6`}>
                <div className="flex items-center">
                  <div className="p-3 bg-purple-100 rounded-lg">
                    <Activity className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {loading ? '...' : (applicationStats?.conversionRate || 0)}%
                    </p>
                    <p className="text-xs text-purple-600 mt-1">Applications to hires</p>
                  </div>
                </div>
              </div>

              <div className={`${shadows.card} p-6`}>
                <div className="flex items-center">
                  <div className="p-3 bg-orange-100 rounded-lg">
                    <Clock className="h-6 w-6 text-orange-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Avg. Score</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {loading ? '...' : (applicationStats?.averageScore || 0)}
                    </p>
                    <p className="text-xs text-orange-600 mt-1">Application quality</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions & Recent Activity - Mobile Responsive */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Quick Actions */}
              <div className={`${shadows.card} flex flex-col h-[320px]`}>
                <div className="p-6 pb-4 border-b border-slate-100">
                  <h3 className="text-lg font-semibold text-slate-900">Quick Actions</h3>
                  <p className="text-sm text-slate-600 mt-1">Common tasks and shortcuts</p>
                </div>
                <div className="p-6 space-y-3 flex-1">
                  <button
                    onClick={() => setActiveTab('applications')}
                    className="w-full text-left p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 hover:border-blue-200 transition-all duration-200"
                  >
                    <div className="flex items-center">
                      <Users className="h-5 w-5 text-blue-600 mr-3 flex-shrink-0" />
                      <div className="min-w-0 flex-1">
                        <p className="font-medium text-slate-900">Review New Applications</p>
                        <p className="text-sm text-slate-600 truncate">
                          {applicationStats?.newApplications || 0} pending review
                        </p>
                      </div>
                    </div>
                  </button>
                  <button
                    onClick={() => setActiveTab('forms')}
                    className="w-full text-left p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-100 hover:border-green-200 transition-all duration-200"
                  >
                    <div className="flex items-center">
                      <Edit className="h-5 w-5 text-green-600 mr-3 flex-shrink-0" />
                      <div className="min-w-0 flex-1">
                        <p className="font-medium text-slate-900">Manage Application Forms</p>
                        <p className="text-sm text-slate-600 truncate">Build and customize forms</p>
                      </div>
                    </div>
                  </button>
                  <button
                    onClick={() => setActiveTab('performance')}
                    className="w-full text-left p-4 bg-gradient-to-r from-purple-50 to-violet-50 rounded-lg border border-purple-100 hover:border-purple-200 transition-all duration-200"
                  >
                    <div className="flex items-center">
                      <BarChart3 className="h-5 w-5 text-purple-600 mr-3 flex-shrink-0" />
                      <div className="min-w-0 flex-1">
                        <p className="font-medium text-slate-900">View Performance Analytics</p>
                        <p className="text-sm text-slate-600 truncate">Insights and trends</p>
                      </div>
                    </div>
                  </button>
                </div>
              </div>

              {/* Recent Applications */}
              <div className={`${shadows.card} flex flex-col h-[320px]`}>
                <div className="p-6 pb-4 border-b border-slate-100">
                  <h3 className="text-lg font-semibold text-slate-900">Recent Applications</h3>
                  <p className="text-sm text-slate-600 mt-1">Latest submissions</p>
                </div>
                <div className="p-6 space-y-3 flex-1 overflow-y-auto">
                  {loading ? (
                    <div className="text-center text-slate-500 py-8 text-base">Loading applications...</div>
                  ) : applicationStats?.recentApplications && applicationStats.recentApplications.length > 0 ? (
                    applicationStats.recentApplications.slice(0, 4).map((app, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gradient-to-r from-slate-50 to-gray-50 rounded-lg border border-slate-100 hover:border-slate-200 transition-colors">
                        <div className="min-w-0 flex-1">
                          <p className="font-medium text-slate-900 truncate">{app.candidateName}</p>
                          <p className="text-sm text-slate-600 truncate">{app.jobTitle}</p>
                          <p className="text-xs text-slate-400">{formatDate(app.submittedAt)}</p>
                        </div>
                        <div className="flex items-center space-x-3 ml-3 flex-shrink-0">
                          <Badge className={getStatusColor(app.status)}>
                            {app.status}
                          </Badge>
                          {app.score && (
                            <span className="text-sm font-medium text-slate-600">{app.score}</span>
                          )}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center text-slate-500 py-8 text-base">
                      <FileText className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                      <p>No recent applications</p>
                      <p className="text-sm">Applications will appear here once submitted</p>
                    </div>
                  )}
                </div>
                <div className="p-6 pt-4 border-t border-slate-100">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setActiveTab('applications')}
                    className="justify-start w-fit px-0 text-blue-600 hover:text-blue-700 font-medium"
                  >
                    View all applications
                    <ExternalLink className="ml-1 h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Timeframe Selector */}
            <div className={`${shadows.card} p-4`}>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                  <h4 className="text-sm font-medium text-slate-900">Data Timeframe</h4>
                  <p className="text-xs text-slate-600">Adjust the time period for metrics</p>
                </div>
                <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Select timeframe" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7d">Last 7 days</SelectItem>
                    <SelectItem value="30d">Last 30 days</SelectItem>
                    <SelectItem value="90d">Last 90 days</SelectItem>
                    <SelectItem value="1y">Last year</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )}

        {/* Applications Tab */}
        {activeTab === 'applications' && (
          <div className="space-y-6">
            {/* Enhanced Filters - Mobile Responsive */}
            <div className={`${shadows.card} p-4`}>
              <div className="flex flex-col gap-4">
                <div className="flex flex-col sm:flex-row gap-3">
                  <div className="relative flex-1 min-w-0">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                    <Input
                      type="text"
                      placeholder="Search applications..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 pr-4 py-2 w-full"
                    />
                  </div>
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Select value={positionFilter} onValueChange={setPositionFilter}>
                      <SelectTrigger className="w-full sm:w-48">
                        <SelectValue placeholder="All Positions" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Positions</SelectItem>
                        <SelectItem value="product-manager">Product Manager</SelectItem>
                        <SelectItem value="ux-designer">UX Designer</SelectItem>
                        <SelectItem value="software-engineer">Software Engineer</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger className="w-full sm:w-40">
                        <SelectValue placeholder="All Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Status</SelectItem>
                        <SelectItem value="applied">Applied</SelectItem>
                        <SelectItem value="review">Review</SelectItem>
                        <SelectItem value="screening">Screening</SelectItem>
                        <SelectItem value="interview">Interview</SelectItem>
                        <SelectItem value="offer">Offer</SelectItem>
                        <SelectItem value="hired">Hired</SelectItem>
                        <SelectItem value="rejected">Rejected</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm" className="border-slate-300 hover:border-blue-500 hover:bg-blue-50 hover:text-blue-600">
                      <Filter className="h-4 w-4 mr-2" />
                      More Filters
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm" className="border-slate-300 hover:border-blue-500 hover:bg-blue-50 hover:text-blue-600">
                          <ChevronDown className="h-4 w-4 mr-2" />
                          View
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem onClick={() => setViewMode('table')}>
                          <List className="h-4 w-4 mr-2" />
                          Table View
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => setViewMode('grid')}>
                          <Grid className="h-4 w-4 mr-2" />
                          Grid View
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  {selectedApplications.length > 0 && (
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-slate-600">
                        {selectedApplications.length} selected
                      </span>
                      <Button variant="outline" size="sm" className="border-slate-300 hover:border-blue-500 hover:bg-blue-50 hover:text-blue-600">
                        Bulk Actions
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Applications Table */}
            <div className={`${shadows.card} overflow-hidden`}>
              <div className="px-6 py-4 border-b border-slate-100">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <div>
                    <h3 className="text-lg font-semibold text-slate-900">
                      All Applications ({applications?.length || 0})
                    </h3>
                    <p className="text-sm text-slate-600 mt-1">Manage and review candidate applications</p>
                  </div>
                  {applications?.length > 0 && (
                    <div className="flex items-center gap-2 text-sm text-slate-600">
                      <span>Showing {applications.length} applications</span>
                    </div>
                  )}
                </div>
              </div>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="border-slate-100">
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedApplications.length === applications?.length && applications.length > 0}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedApplications(applications?.map(app => app.id) || []);
                            } else {
                              setSelectedApplications([]);
                            }
                          }}
                        />
                      </TableHead>
                      <TableHead className="text-slate-700 font-medium">Candidate</TableHead>
                      <TableHead className="text-slate-700 font-medium">Position</TableHead>
                      <TableHead className="text-slate-700 font-medium">Status</TableHead>
                      <TableHead className="text-slate-700 font-medium">Score</TableHead>
                      <TableHead className="text-slate-700 font-medium">Submitted</TableHead>
                      <TableHead className="text-right text-slate-700 font-medium">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8 text-slate-500">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                          <p>Loading applications...</p>
                        </TableCell>
                      </TableRow>
                    ) : applications && applications.length > 0 ? (
                      applications.map((app, index) => (
                        <TableRow key={app.id || index} className="hover:bg-slate-50 border-slate-100">
                          <TableCell>
                            <Checkbox
                              checked={selectedApplications.includes(app.id)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedApplications([...selectedApplications, app.id]);
                                } else {
                                  setSelectedApplications(selectedApplications.filter(id => id !== app.id));
                                }
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <div className="h-10 w-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                                <span className="text-sm font-medium text-white">
                                  {app.candidateName?.split(' ').map(n => n[0]).join('') || 'N/A'}
                                </span>
                              </div>
                              <div className="ml-4 min-w-0">
                                <div className="text-sm font-medium text-slate-900 truncate">{app.candidateName || 'Unknown'}</div>
                                <div className="text-xs text-slate-500 truncate">{app.candidateEmail || ''}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-sm text-slate-900">{app.jobTitle || 'Unknown Position'}</TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(app.status)}>
                              {app.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-sm text-slate-900">
                            {app.score ? `${app.score}/100` : 'N/A'}
                          </TableCell>
                          <TableCell className="text-sm text-slate-500">
                            {app.submittedAt ? formatDate(app.submittedAt) : 'N/A'}
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="hover:bg-slate-100">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => navigate(`/candidates/${app.candidateId}`)}>
                                  <Eye className="h-4 w-4 mr-2" />
                                  View Details
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit Status
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-red-600">
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Reject
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-12 text-slate-500">
                          <FileText className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                          <p className="text-lg font-medium">No applications found</p>
                          <p className="text-sm">Applications will appear here once submitted</p>
                          <Button
                            variant="outline"
                            size="sm"
                            className="mt-4"
                            onClick={() => navigate('/jobs')}
                          >
                            Create Job Posting
                          </Button>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        )}

        {/* Forms Tab */}
        {activeTab === 'forms' && (
          <div className="space-y-6">
            {/* Form Stats - Mobile Responsive */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className={`${shadows.card} p-6`}>
                <div className="flex items-center">
                  <div className="p-3 bg-blue-100 rounded-lg">
                    <FileText className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-slate-600">Total Forms</p>
                    <p className="text-2xl font-bold text-slate-900">{formsLoading ? '...' : forms.length}</p>
                  </div>
                </div>
              </div>
              <div className={`${shadows.card} p-6`}>
                <div className="flex items-center">
                  <div className="p-3 bg-green-100 rounded-lg">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-slate-600">Live Forms</p>
                    <p className="text-2xl font-bold text-slate-900">
                      {formsLoading ? '...' : forms.filter(f => f.status === 'live').length}
                    </p>
                  </div>
                </div>
              </div>
              <div className={`${shadows.card} p-6`}>
                <div className="flex items-center">
                  <div className="p-3 bg-yellow-100 rounded-lg">
                    <AlertCircle className="h-6 w-6 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-slate-600">Draft Forms</p>
                    <p className="text-2xl font-bold text-slate-900">
                      {formsLoading ? '...' : forms.filter(f => f.status === 'draft').length}
                    </p>
                  </div>
                </div>
              </div>
              <div className={`${shadows.card} p-6`}>
                <div className="flex items-center">
                  <div className="p-3 bg-purple-100 rounded-lg">
                    <TrendingUp className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-slate-600">Total Views</p>
                    <p className="text-2xl font-bold text-slate-900">
                      {formsLoading ? '...' : forms.reduce((sum, f) => sum + (f.viewCount || 0), 0)}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Forms List */}
            <div className={`${shadows.card}`}>
              <div className="px-6 py-4 border-b border-slate-100 flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-slate-900 mb-1">Application Forms</h3>
                  <p className="text-sm text-slate-600">Manage and customize application forms</p>
                </div>
                <Button
                  onClick={() => navigate('/jobs')}
                  size="sm"
                  className="mt-3 sm:mt-0 bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create New Form
                </Button>
              </div>
              <div className="divide-y divide-slate-100">
                {formsLoading ? (
                  <div className="text-center py-12 text-slate-500">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p>Loading forms...</p>
                  </div>
                ) : forms.length > 0 ? (
                  forms.map((form) => (
                    <div key={form.id} className="p-6 hover:bg-slate-50 transition-colors">
                      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div className="flex-1 mb-4 lg:mb-0">
                          <div className="flex items-center space-x-3 mb-2">
                            <h4 className="text-lg font-medium text-slate-900">{form.title}</h4>
                            <Badge className={getFormStatusColor(form.status)}>
                              {form.status}
                            </Badge>
                          </div>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-slate-600">
                            <div>
                              <span className="font-medium">Submissions:</span> {form.submissionCount || 0}
                            </div>
                            <div>
                              <span className="font-medium">Views:</span> {form.viewCount || 0}
                            </div>
                            <div>
                              <span className="font-medium">Conversion:</span> {form.conversionRate || 0}%
                            </div>
                            <div>
                              <span className="font-medium">Modified:</span> {new Date(form.updatedAt).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-slate-300 hover:border-blue-500 hover:bg-blue-50 hover:text-blue-600"
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            Preview
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => navigate(`/jobs/${form.jobId}`)}
                            className="border-slate-300 hover:border-blue-500 hover:bg-blue-50 hover:text-blue-600"
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-slate-300 hover:border-blue-500 hover:bg-blue-50 hover:text-blue-600"
                          >
                            <Share2 className="h-4 w-4 mr-2" />
                            Share
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-12 text-slate-500">
                    <FileText className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                    <p className="text-lg font-medium">No forms created yet</p>
                    <p className="text-sm">Create your first job posting to generate application forms</p>
                    <Button
                      onClick={() => navigate('/jobs')}
                      size="sm"
                      className="mt-4 bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Create Job Posting
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Performance Tab */}
        {activeTab === 'performance' && (
          <div className="space-y-6">
            {/* Time Range Selector */}
            <div className={`${shadows.card} p-4`}>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                  <h3 className="text-lg font-semibold text-slate-900">Performance Analytics</h3>
                  <p className="text-sm text-slate-600 mt-1">Track application metrics and trends</p>
                </div>
                <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Select timeframe" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7d">Last 7 days</SelectItem>
                    <SelectItem value="30d">Last 30 days</SelectItem>
                    <SelectItem value="90d">Last 90 days</SelectItem>
                    <SelectItem value="1y">Last year</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Analytics Grid - Mobile Responsive */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Application Sources */}
              <div className={`${shadows.card} p-6`}>
                <h4 className="text-lg font-semibold text-slate-900 mb-4">Application Sources</h4>
                <div className="space-y-4">
                  {analyticsLoading ? (
                    <div className="text-center py-8 text-slate-500">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                      <p>Loading analytics...</p>
                    </div>
                  ) : analyticsData?.sourceStats?.length > 0 ? (
                    analyticsData.sourceStats.map((source, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div
                            className="w-3 h-3 rounded-full mr-3"
                            style={{backgroundColor: `hsl(${index * 60}, 70%, 50%)`}}
                          ></div>
                          <span className="text-sm font-medium text-slate-700">{source.source}</span>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-semibold text-slate-900">{source.applications}</div>
                          <div className="text-xs text-slate-500">{source.percentage}%</div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-slate-500">
                      <PieChart className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                      <p>No source data available</p>
                      <p className="text-sm">Data will appear here once applications are submitted</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Application Status Distribution */}
              <div className={`${shadows.card} p-6`}>
                <h4 className="text-lg font-semibold text-slate-900 mb-4">Status Distribution</h4>
                <div className="space-y-4">
                  {analyticsLoading ? (
                    <div className="text-center py-8 text-slate-500">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                      <p>Loading analytics...</p>
                    </div>
                  ) : analyticsData?.applicationsByStatus?.length > 0 ? (
                    analyticsData.applicationsByStatus.map((item, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div
                            className="w-3 h-3 rounded-full mr-3"
                            style={{backgroundColor: `hsl(${120 - index * 30}, 70%, 50%)`}}
                          ></div>
                          <span className="text-sm font-medium text-slate-700 capitalize">{item.status}</span>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-semibold text-slate-900">{item.count}</div>
                          <div className="text-xs text-slate-500">{item.percentage}%</div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-slate-500">
                      <BarChart3 className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                      <p>No status data available</p>
                      <p className="text-sm">Data will appear here once applications are submitted</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Performance Trends */}
            <div className={`${shadows.card} p-6`}>
              <h4 className="text-lg font-semibold text-slate-900 mb-4">Application Trends</h4>
              <div className="h-64 flex items-center justify-center text-slate-500">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 mx-auto mb-2 text-slate-400" />
                  <p className="text-lg font-medium">Chart visualization</p>
                  <p className="text-sm">Connected to analytics API endpoint</p>
                  <p className="text-xs text-slate-400 mt-2">
                    {analyticsData ? `${analyticsData.totalApplications} total applications` : 'No data available'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Applications;
