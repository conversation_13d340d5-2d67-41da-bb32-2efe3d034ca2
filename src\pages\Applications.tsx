import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Plus,
  Settings,
  Eye,
  Users,
  FileText,
  BarChart3,
  Filter,
  Search,
  Calendar,
  Mail,
  Download,
  Star,
  Clock,
  AlertCircle,
  Grid,
  List,
  CheckCircle,
  Briefcase,
  MoreHorizontal,
  TrendingUp,
  TrendingDown,
  Activity,
  Edit,
  Share2,
  PieChart,
  RefreshCw,
  ChevronDown,
  SortAsc,
  SortDesc,
  Trash2,
  Copy,
  ExternalLink
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { shadows } from '@/components/ui/shadow';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import { useApplications } from '@/hooks/useApplications';
import { useDashboardStats } from '@/hooks/useDashboardStats';
import { useAnalytics } from '@/hooks/useAnalytics';
import { applicationApi } from '@/services/api';
import { formApi } from '@/services/api';
import { ApplicationFormBuilder } from '@/components/ApplicationFormBuilder';
import { PublicApplicationForm } from '@/components/PublicApplicationForm';
import { StandardApplicationForm } from '@/components/StandardApplicationForm';

// Types
interface ApplicationFormSchema {
  id: string;
  title: string;
  description?: string;
  fields: any[];
  status: 'draft' | 'live' | 'archived';
  submissionCount?: number;
  viewCount?: number;
  createdAt: string;
  updatedAt: string;
}

interface Application {
  id: string;
  candidateName: string;
  jobTitle: string;
  status: string;
  score?: number;
  submittedAt: string;
  formId: string;
}

const Applications = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  
  // Enhanced state management for improved UX
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedTimeframe, setSelectedTimeframe] = useState('30d');
  const [formSchema, setFormSchema] = useState<ApplicationFormSchema | null>(null);
  const [showFormBuilder, setShowFormBuilder] = useState(false);
  const [showPublicForm, setShowPublicForm] = useState(false);
  const [applicationStats, setApplicationStats] = useState<any>(null);
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('table');
  const [forms, setForms] = useState<any[]>([]);
  const [formsLoading, setFormsLoading] = useState(false);
  
  // Enhanced filtering and search state
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [positionFilter, setPositionFilter] = useState('all');
  const [selectedApplications, setSelectedApplications] = useState<string[]>([]);
  const [sortField, setSortField] = useState('submittedAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [refreshing, setRefreshing] = useState(false);

  // Hooks for data fetching
  const { data: dashboardStats, loading: statsLoading } = useDashboardStats();
  const { data: sourceData, loading: sourceLoading } = useAnalytics();

  // Get the first available job or use default
  const defaultJob = {
    id: 'default',
    title: 'General Application',
    department: 'Various',
    location: 'Remote',
    type: 'Full-time'
  };

  // Utility functions for enhanced UX
  const getStatusColor = (status: string) => {
    const colors = {
      'applied': 'bg-blue-100 text-blue-800',
      'review': 'bg-yellow-100 text-yellow-800',
      'screening': 'bg-purple-100 text-purple-800',
      'interview': 'bg-indigo-100 text-indigo-800',
      'offer': 'bg-green-100 text-green-800',
      'hired': 'bg-emerald-100 text-emerald-800',
      'rejected': 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getFormStatusColor = (status: string) => {
    return status === 'live' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      // Reload all data
      const [statsResponse, formsResponse] = await Promise.all([
        applicationApi.getStats(),
        formApi.getForms({ limit: 50 })
      ]);
      
      if (statsResponse) {
        const mappedStats = {
          total: statsResponse.totalApplications || 0,
          new: statsResponse.newApplications || 0,
          conversionRate: statsResponse.conversionRate || 0,
          averageScore: statsResponse.averageScore || 0,
          topSources: statsResponse.sourceStats || [],
          applicationsByStatus: statsResponse.applicationsByStatus || [],
          recentApplications: statsResponse.recentApplications?.map(app => ({
            name: app.candidateName,
            position: app.jobTitle,
            time: new Date(app.submittedAt).toLocaleString(),
            score: app.score || 0,
            status: app.status
          })) || []
        };
        setApplicationStats(mappedStats);
      }
      
      if (formsResponse?.forms) {
        setForms(formsResponse.forms);
      }
      
      toast({
        title: 'Data Refreshed',
        description: 'All data has been updated successfully.',
        variant: 'default',
      });
    } catch (error) {
      console.error('Failed to refresh data:', error);
      toast({
        title: 'Refresh Failed',
        description: 'Failed to refresh data. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setRefreshing(false);
    }
  }, [toast]);

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      try {
        setFormsLoading(true);
        
        const [statsResponse, formsResponse] = await Promise.all([
          applicationApi.getStats(),
          formApi.getForms({ limit: 50 })
        ]);
        
        if (statsResponse) {
          const mappedStats = {
            total: statsResponse.totalApplications || 0,
            new: statsResponse.newApplications || 0,
            conversionRate: statsResponse.conversionRate || 0,
            averageScore: statsResponse.averageScore || 0,
            topSources: statsResponse.sourceStats || [],
            applicationsByStatus: statsResponse.applicationsByStatus || [],
            recentApplications: statsResponse.recentApplications?.map(app => ({
              name: app.candidateName,
              position: app.jobTitle,
              time: new Date(app.submittedAt).toLocaleString(),
              score: app.score || 0,
              status: app.status
            })) || []
          };
          setApplicationStats(mappedStats);
        }
        
        if (formsResponse?.forms) {
          setForms(formsResponse.forms);
        }
      } catch (error) {
        console.error('Failed to load data:', error);
        toast({
          title: 'Loading Error',
          description: 'Failed to load application data. Please refresh the page.',
          variant: 'destructive',
        });
      } finally {
        setFormsLoading(false);
      }
    };

    loadData();
  }, [toast]);

  // Event handlers
  const handlePreviewForm = (form: ApplicationFormSchema) => {
    setFormSchema(form);
    setShowPublicForm(true);
  };

  const handleCreateForm = () => {
    setFormSchema(null);
    setShowFormBuilder(true);
  };

  const handleEditForm = (form: ApplicationFormSchema) => {
    setFormSchema(form);
    setShowFormBuilder(true);
  };

  if (statsLoading && !applicationStats) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Enhanced Header */}
      <div className="bg-white border-b border-gray-200 px-4 lg:px-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between py-4">
          <div className="mb-4 sm:mb-0">
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
              <FileText className="h-6 w-6 text-blue-600" />
              Application Management
            </h1>
            <p className="text-sm text-gray-600 mt-1">Monitor applications, manage forms, and track performance</p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3">
            <Button 
              variant="outline" 
              onClick={handleRefresh}
              disabled={refreshing}
              className="inline-flex items-center"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              {refreshing ? 'Refreshing...' : 'Refresh'}
            </Button>
            <Button 
              variant="outline"
              className="inline-flex items-center"
            >
              <Download className="h-4 w-4 mr-2" />
              Export Data
            </Button>
            <Button 
              onClick={() => setShowFormBuilder(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white inline-flex items-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create New Form
            </Button>
          </div>
        </div>

        {/* Enhanced Navigation Tabs */}
        <div className="flex space-x-8 overflow-x-auto">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'applications', label: 'Applications', icon: FileText },
            { id: 'forms', label: 'Form Management', icon: Edit },
            { id: 'analytics', label: 'Performance', icon: TrendingUp }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Content */}
      <div className="px-4 lg:px-6 py-6">
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center">
                  <div className="p-3 bg-blue-100 rounded-lg">
                    <FileText className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Applications</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {statsLoading ? '...' : (applicationStats?.total || dashboardStats?.totalApplications || 0)}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">Last 30 days</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center">
                  <div className="p-3 bg-green-100 rounded-lg">
                    <TrendingUp className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">New Applications</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {statsLoading ? '...' : (applicationStats?.new || dashboardStats?.newApplications || 0)}
                    </p>
                    <p className="text-xs text-green-600 mt-1">+5 this week</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center">
                  <div className="p-3 bg-purple-100 rounded-lg">
                    <Activity className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {statsLoading ? '...' : (applicationStats?.conversionRate || dashboardStats?.conversionRate || 0)}%
                    </p>
                    <p className="text-xs text-purple-600 mt-1">+2.1% from last month</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center">
                  <div className="p-3 bg-orange-100 rounded-lg">
                    <Clock className="h-6 w-6 text-orange-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Avg. Completion Time</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {statsLoading ? '...' : (applicationStats?.averageScore || dashboardStats?.averageScore || 8.5)}m
                    </p>
                    <p className="text-xs text-orange-600 mt-1">-1.2m improvement</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions & Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Quick Actions */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div className="space-y-3">
                  <button
                    onClick={() => setActiveTab('applications')}
                    className="w-full text-left p-4 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center">
                      <Users className="h-5 w-5 text-blue-600 mr-3" />
                      <div>
                        <p className="font-medium text-gray-900">Review New Applications</p>
                        <p className="text-sm text-gray-500">
                          {applicationStats?.new || dashboardStats?.newApplications || 0} pending review
                        </p>
                      </div>
                    </div>
                  </button>
                  <button
                    onClick={() => setActiveTab('forms')}
                    className="w-full text-left p-4 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center">
                      <Edit className="h-5 w-5 text-green-600 mr-3" />
                      <div>
                        <p className="font-medium text-gray-900">Create Application Form</p>
                        <p className="text-sm text-gray-500">Build custom forms for positions</p>
                      </div>
                    </div>
                  </button>
                  <button
                    onClick={() => setActiveTab('analytics')}
                    className="w-full text-left p-4 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center">
                      <BarChart3 className="h-5 w-5 text-purple-600 mr-3" />
                      <div>
                        <p className="font-medium text-gray-900">View Detailed Analytics</p>
                        <p className="text-sm text-gray-500">Performance insights and trends</p>
                      </div>
                    </div>
                  </button>
                </div>
              </div>

              {/* Recent Applications */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Applications</h3>
                <div className="space-y-4">
                  {applicationStats?.recentApplications && applicationStats.recentApplications.length > 0 ? (
                    applicationStats.recentApplications.slice(0, 4).map((app, index) => (
                      <div key={index} className="flex items-center justify-between p-3 rounded-lg border border-gray-100">
                        <div className="flex-1">
                          <p className="font-medium text-gray-900">{app.name}</p>
                          <p className="text-sm text-gray-500">{app.position}</p>
                          <p className="text-xs text-gray-400">{formatDate(app.time)}</p>
                        </div>
                        <div className="flex items-center space-x-3">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(app.status)}`}>
                            {app.status}
                          </span>
                          <span className="text-sm font-medium text-gray-600">Score: {app.score}</span>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <p>No recent applications</p>
                      <p className="text-sm">Applications will appear here once submitted</p>
                    </div>
                  )}
                </div>
                <button
                  onClick={() => setActiveTab('applications')}
                  className="w-full mt-4 py-2 text-sm text-blue-600 hover:text-blue-700 font-medium"
                >
                  View All Applications →
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Applications Tab */}
        {activeTab === 'applications' && (
          <div className="space-y-6">
            {/* Enhanced Filters */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="flex flex-col sm:flex-row gap-3">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      type="text"
                      placeholder="Search applications..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 pr-4 py-2 w-64"
                    />
                  </div>
                  <Select value={positionFilter} onValueChange={setPositionFilter}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="All Positions" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Positions</SelectItem>
                      <SelectItem value="product-manager">Product Manager</SelectItem>
                      <SelectItem value="ux-designer">UX Designer</SelectItem>
                      <SelectItem value="software-engineer">Software Engineer</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="All Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="applied">Applied</SelectItem>
                      <SelectItem value="review">Review</SelectItem>
                      <SelectItem value="screening">Screening</SelectItem>
                      <SelectItem value="interview">Interview</SelectItem>
                      <SelectItem value="offer">Offer</SelectItem>
                      <SelectItem value="hired">Hired</SelectItem>
                      <SelectItem value="rejected">Rejected</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    More Filters
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm">
                        <ChevronDown className="h-4 w-4 mr-2" />
                        View
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => setViewMode('table')}>
                        <List className="h-4 w-4 mr-2" />
                        Table View
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setViewMode('grid')}>
                        <Grid className="h-4 w-4 mr-2" />
                        Grid View
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </div>

            {/* Applications Table */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">
                  All Applications ({applicationStats?.recentApplications?.length || 0})
                </h3>
              </div>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox />
                      </TableHead>
                      <TableHead>Candidate</TableHead>
                      <TableHead>Position</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Score</TableHead>
                      <TableHead>Submitted</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {applicationStats?.recentApplications && applicationStats.recentApplications.length > 0 ? (
                      applicationStats.recentApplications.map((app, index) => (
                        <TableRow key={index} className="hover:bg-gray-50">
                          <TableCell>
                            <Checkbox />
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <div className="h-10 w-10 bg-gray-300 rounded-full flex items-center justify-center">
                                <span className="text-sm font-medium text-gray-700">
                                  {app.name.split(' ').map(n => n[0]).join('')}
                                </span>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">{app.name}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-sm text-gray-900">{app.position}</TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(app.status)}>
                              {app.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-sm text-gray-900">{app.score}/100</TableCell>
                          <TableCell className="text-sm text-gray-500">{formatDate(app.time)}</TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem>
                                  <Eye className="h-4 w-4 mr-2" />
                                  View Details
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit Status
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-red-600">
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Reject
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                          <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                          <p>No applications found</p>
                          <p className="text-sm">Applications will appear here once submitted</p>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        )}

        {/* Forms Tab */}
        {activeTab === 'forms' && (
          <div className="space-y-6">
            {/* Form Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center">
                  <FileText className="h-8 w-8 text-blue-600" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-600">Total Forms</p>
                    <p className="text-2xl font-bold text-gray-900">{forms.length}</p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-600">Live Forms</p>
                    <p className="text-2xl font-bold text-gray-900">{forms.filter(f => f.status === 'live').length}</p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center">
                  <AlertCircle className="h-8 w-8 text-yellow-600" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-600">Draft Forms</p>
                    <p className="text-2xl font-bold text-gray-900">{forms.filter(f => f.status === 'draft').length}</p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center">
                  <TrendingUp className="h-8 w-8 text-purple-600" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-600">Total Views</p>
                    <p className="text-2xl font-bold text-gray-900">{forms.reduce((sum, f) => sum + (f.viewCount || 0), 0)}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Forms List */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200 flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <h3 className="text-lg font-semibold text-gray-900 mb-2 sm:mb-0">Application Forms</h3>
                <Button
                  onClick={() => setShowFormBuilder(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white inline-flex items-center"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create New Form
                </Button>
              </div>
              <div className="divide-y divide-gray-200">
                {forms.map((form) => (
                  <div key={form.id} className="p-6 hover:bg-gray-50">
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                      <div className="flex-1 mb-4 lg:mb-0">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="text-lg font-medium text-gray-900">{form.title}</h4>
                          <Badge className={getFormStatusColor(form.status)}>
                            {form.status}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                          <div>
                            <span className="font-medium">Submissions:</span> {form.submissionCount || 0}
                          </div>
                          <div>
                            <span className="font-medium">Views:</span> {form.viewCount || 0}
                          </div>
                          <div>
                            <span className="font-medium">Conversion:</span> {
                              form.viewCount > 0 ? ((form.submissionCount || 0) / form.viewCount * 100).toFixed(1) : 0
                            }%
                          </div>
                          <div>
                            <span className="font-medium">Modified:</span> {new Date(form.updatedAt).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePreviewForm(form)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          Preview
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setFormSchema(form);
                            setShowFormBuilder(true);
                          }}
                        >
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </Button>
                        <Button variant="outline" size="sm">
                          <Share2 className="h-4 w-4 mr-2" />
                          Share
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
                {forms.length === 0 && (
                  <div className="text-center py-12 text-gray-500">
                    <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p className="text-lg font-medium">No forms created yet</p>
                    <p className="text-sm">Create your first application form to get started</p>
                    <Button
                      onClick={() => setShowFormBuilder(true)}
                      className="mt-4 bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Create Form
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Analytics Tab */}
        {activeTab === 'analytics' && (
          <div className="space-y-6">
            {/* Time Range Selector */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Performance Analytics</h3>
                <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Select timeframe" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7d">Last 7 days</SelectItem>
                    <SelectItem value="30d">Last 30 days</SelectItem>
                    <SelectItem value="90d">Last 90 days</SelectItem>
                    <SelectItem value="1y">Last year</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Analytics Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Traffic Sources */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">Application Sources</h4>
                <div className="space-y-4">
                  {sourceData?.sourceEffectiveness?.map((source, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div
                          className="w-3 h-3 rounded-full mr-3"
                          style={{backgroundColor: `hsl(${index * 60}, 70%, 50%)`}}
                        ></div>
                        <span className="text-sm font-medium text-gray-700">{source.source}</span>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-semibold text-gray-900">{source.applications}</div>
                        <div className="text-xs text-gray-500">
                          {Math.round((source.applications / sourceData.totalApplications) * 100)}%
                        </div>
                      </div>
                    </div>
                  )) || (
                    <div className="text-center py-8 text-gray-500">
                      <PieChart className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <p>No source data available</p>
                      <p className="text-sm">Data will appear here once applications are submitted</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Completion Quality */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">Form Completion Quality</h4>
                <div className="space-y-4">
                  {applicationStats?.applicationsByStatus?.map((item, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div
                          className="w-3 h-3 rounded-full mr-3"
                          style={{backgroundColor: `hsl(${120 - index * 30}, 70%, 50%)`}}
                        ></div>
                        <span className="text-sm font-medium text-gray-700">{item.status}</span>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-semibold text-gray-900">{item._count}</div>
                        <div className="text-xs text-gray-500">{item.percentage}%</div>
                      </div>
                    </div>
                  )) || (
                    <div className="text-center py-8 text-gray-500">
                      <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <p>No completion data available</p>
                      <p className="text-sm">Data will appear here once applications are submitted</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Performance Trends */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Application Trends</h4>
              <div className="h-64 flex items-center justify-center text-gray-500">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 mx-auto mb-2 text-gray-400" />
                  <p>Chart visualization would be rendered here</p>
                  <p className="text-sm">Connected to analytics API endpoint</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Modals */}
        {showFormBuilder && (
          <ApplicationFormBuilder
            formSchema={formSchema}
            onClose={() => {
              setShowFormBuilder(false);
              setFormSchema(null);
            }}
            onSave={(savedForm) => {
              // Handle form save
              setShowFormBuilder(false);
              setFormSchema(null);
              handleRefresh();
            }}
          />
        )}

        {showPublicForm && formSchema && (
          <PublicApplicationForm
            formSchema={formSchema}
            onClose={() => {
              setShowPublicForm(false);
              setFormSchema(null);
            }}
          />
        )}
      </div>
    </div>
  );
};

export default Applications;
