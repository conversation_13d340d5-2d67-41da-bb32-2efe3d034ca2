import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Plus,
  Eye,
  Users,
  FileText,
  BarChart3,
  Filter,
  Search,
  Download,
  Clock,
  TrendingUp,
  Activity,
  Edit,
  RefreshCw,
  ChevronDown,
  MoreHorizontal,
  Trash2,
  ExternalLink,
  List,
  Grid
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { shadows } from '@/components/ui/shadow';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import { useResponsiveLayout } from '@/hooks/useResponsiveLayout';
import { applicationApi, formApi, analyticsApi } from '@/services/api';

// Types
interface Application {
  id: string;
  candidateName: string;
  candidateEmail?: string;
  jobTitle: string;
  status: string;
  score?: number;
  submittedAt: string;
  formId: string;
  candidateId?: string;
}

const ApplicationsTest = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  // Enhanced responsive layout management
  const {
    isMobile,
    isTablet,
    shouldUseCardLayout,
    getColumnsForDevice,
    getGridClasses,
    shouldStackFormButtons,
    getModalSize
  } = useResponsiveLayout();

  // Enhanced state management for improved UX
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedTimeframe, setSelectedTimeframe] = useState('30d');
  const [applicationStats, setApplicationStats] = useState<any>(null);
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('table');
  const [forms, setForms] = useState<any[]>([]);
  const [applications, setApplications] = useState<any[]>([]);
  const [analyticsData, setAnalyticsData] = useState<any>(null);

  // Loading states
  const [loading, setLoading] = useState(true);
  const [formsLoading, setFormsLoading] = useState(false);
  const [analyticsLoading, setAnalyticsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Enhanced filtering and search state
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [positionFilter, setPositionFilter] = useState('all');
  const [selectedApplications, setSelectedApplications] = useState<string[]>([]);
  const [sortField, setSortField] = useState('submittedAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Utility functions for enhanced UX
  const getStatusColor = (status: string) => {
    const colors = {
      'applied': 'bg-blue-100 text-blue-800',
      'review': 'bg-yellow-100 text-yellow-800',
      'screening': 'bg-purple-100 text-purple-800',
      'interview': 'bg-indigo-100 text-indigo-800',
      'offer': 'bg-green-100 text-green-800',
      'hired': 'bg-emerald-100 text-emerald-800',
      'rejected': 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Load data based on active tab
  const loadData = useCallback(async (tabName: string) => {
    try {
      setLoading(true);

      switch (tabName) {
        case 'dashboard':
          const overviewResponse = await applicationApi.getOverview({ timeframe: selectedTimeframe });
          setApplicationStats(overviewResponse);
          break;

        case 'applications':
          const applicationsResponse = await applicationApi.getApplications({
            limit: 50,
            status: statusFilter !== 'all' ? statusFilter : undefined,
            search: searchQuery || undefined
          });
          setApplications(applicationsResponse.applications || []);
          break;

        case 'forms':
          setFormsLoading(true);
          const formsResponse = await formApi.getForms({ limit: 50 });
          setForms(formsResponse.forms || []);
          setFormsLoading(false);
          break;

        case 'performance':
          setAnalyticsLoading(true);
          const analyticsResponse = await analyticsApi.getPerformanceAnalytics({ timeframe: selectedTimeframe });
          setAnalyticsData(analyticsResponse);
          setAnalyticsLoading(false);
          break;
      }
    } catch (error) {
      console.error(`Failed to load ${tabName} data:`, error);
      toast({
        title: 'Loading Error',
        description: `Failed to load ${tabName} data. Please try again.`,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [selectedTimeframe, statusFilter, searchQuery, toast]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await loadData(activeTab);
      toast({
        title: 'Data Refreshed',
        description: 'All data has been updated successfully.',
        variant: 'default',
      });
    } catch (error) {
      console.error('Failed to refresh data:', error);
      toast({
        title: 'Refresh Failed',
        description: 'Failed to refresh data. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setRefreshing(false);
    }
  }, [activeTab, loadData, toast]);

  // Load initial data and reload when tab changes
  useEffect(() => {
    loadData(activeTab);
  }, [activeTab, loadData]);

  // Reload data when timeframe changes
  useEffect(() => {
    if (activeTab === 'dashboard' || activeTab === 'performance') {
      loadData(activeTab);
    }
  }, [selectedTimeframe, activeTab, loadData]);

  if (loading && !applicationStats && !applications.length && !forms.length) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 -m-6 p-6 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 -m-6 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <div className="flex items-center gap-3">
              <FileText className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold text-slate-900">Application Management</h1>
                <p className="text-slate-600">Monitor applications, manage forms, and track performance</p>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
              className="border-gray-300 hover:border-blue-500 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              {refreshing ? 'Refreshing...' : 'Refresh'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={async () => {
                try {
                  const exportData = await applicationApi.exportApplications();
                  toast({
                    title: 'Export Started',
                    description: `Exporting ${exportData.count} applications...`,
                  });
                } catch (error) {
                  toast({
                    title: 'Export Failed',
                    description: 'Failed to export data. Please try again.',
                    variant: 'destructive',
                  });
                }
              }}
              className="border-gray-300 hover:border-blue-500 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200"
            >
              <Download className="h-4 w-4 mr-2" />
              Export Data
            </Button>
            <Button
              size="sm"
              onClick={() => navigate('/jobs')}
              className="bg-blue-600 hover:bg-blue-700 text-white shadow-sm transition-all duration-200"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Job
            </Button>
          </div>
        </div>

        {/* Enhanced Navigation Tabs - Mobile Responsive */}
        <div className="bg-white rounded-lg border border-slate-200 shadow-sm">
          <div className="p-1">
            <div className="flex space-x-1">
              {[
                { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
                { id: 'applications', label: 'Applications', icon: FileText, count: applications?.length },
                { id: 'forms', label: 'Forms', icon: Edit, count: forms?.length },
                { id: 'performance', label: 'Performance', icon: TrendingUp }
              ].map((tab) => {
                const Icon = tab.icon;
                const isActive = activeTab === tab.id;

                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 px-4 py-3 rounded-lg font-medium text-sm transition-all duration-200 ${
                      isActive
                        ? 'bg-blue-600 text-white shadow-sm'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    {Icon && <Icon className="h-4 w-4" />}
                    <span>{tab.label}</span>
                    {tab.count !== undefined && (
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        isActive
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 text-gray-700'
                      }`}>
                        {tab.count}
                      </span>
                    )}
                  </button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Dashboard Tab */}
        {activeTab === 'dashboard' && (
          <div className="bg-white rounded-lg border border-slate-200 shadow-sm p-6 space-y-6">
            {/* Key Metrics - Mobile Responsive Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className={`${shadows.card} p-6`}>
                <div className="flex items-center">
                  <div className="p-3 bg-blue-100 rounded-lg">
                    <FileText className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Applications</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {loading ? '...' : (applicationStats?.totalApplications || 0)}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {selectedTimeframe === '7d' ? 'Last 7 days' :
                       selectedTimeframe === '30d' ? 'Last 30 days' :
                       selectedTimeframe === '90d' ? 'Last 90 days' : 'Last year'}
                    </p>
                  </div>
                </div>
              </div>

              <div className={`${shadows.card} p-6`}>
                <div className="flex items-center">
                  <div className="p-3 bg-green-100 rounded-lg">
                    <TrendingUp className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">New Applications</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {loading ? '...' : (applicationStats?.newApplications || 0)}
                    </p>
                    <p className={`text-xs mt-1 ${
                      (applicationStats?.growthRate || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {(applicationStats?.growthRate || 0) >= 0 ? '+' : ''}{applicationStats?.growthRate || 0}% from last period
                    </p>
                  </div>
                </div>
              </div>

              <div className={`${shadows.card} p-6`}>
                <div className="flex items-center">
                  <div className="p-3 bg-purple-100 rounded-lg">
                    <Activity className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Conversion Rate</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {loading ? '...' : (applicationStats?.conversionRate || 0)}%
                    </p>
                    <p className="text-xs text-purple-600 mt-1">Applications to hires</p>
                  </div>
                </div>
              </div>

              <div className={`${shadows.card} p-6`}>
                <div className="flex items-center">
                  <div className="p-3 bg-orange-100 rounded-lg">
                    <Clock className="h-6 w-6 text-orange-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Avg. Score</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {loading ? '...' : (applicationStats?.averageScore || 0)}
                    </p>
                    <p className="text-xs text-orange-600 mt-1">Application quality</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Timeframe Selector */}
            <div className={`${shadows.card} p-4`}>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                  <h4 className="text-sm font-medium text-slate-900">Data Timeframe</h4>
                  <p className="text-xs text-slate-600">Adjust the time period for metrics</p>
                </div>
                <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Select timeframe" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7d">Last 7 days</SelectItem>
                    <SelectItem value="30d">Last 30 days</SelectItem>
                    <SelectItem value="90d">Last 90 days</SelectItem>
                    <SelectItem value="1y">Last year</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )}

        {/* Applications Tab */}
        {activeTab === 'applications' && (
          <div className="space-y-6">
            <div className={`${shadows.card} p-4`}>
              <div className="flex flex-col gap-4">
                <div className="flex flex-col sm:flex-row gap-3">
                  <div className="relative flex-1 min-w-0">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                    <Input
                      type="text"
                      placeholder="Search applications..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 pr-4 py-2 w-full"
                    />
                  </div>
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Select value={positionFilter} onValueChange={setPositionFilter}>
                      <SelectTrigger className="w-full sm:w-48">
                        <SelectValue placeholder="All Positions" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Positions</SelectItem>
                        <SelectItem value="product-manager">Product Manager</SelectItem>
                        <SelectItem value="ux-designer">UX Designer</SelectItem>
                        <SelectItem value="software-engineer">Software Engineer</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger className="w-full sm:w-40">
                        <SelectValue placeholder="All Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Status</SelectItem>
                        <SelectItem value="applied">Applied</SelectItem>
                        <SelectItem value="review">Review</SelectItem>
                        <SelectItem value="screening">Screening</SelectItem>
                        <SelectItem value="interview">Interview</SelectItem>
                        <SelectItem value="offer">Offer</SelectItem>
                        <SelectItem value="hired">Hired</SelectItem>
                        <SelectItem value="rejected">Rejected</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </div>

            {/* Applications Table */}
            <div className={`${shadows.card} overflow-hidden`}>
              <div className="px-6 py-4 border-b border-slate-100">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <div>
                    <h3 className="text-lg font-semibold text-slate-900">
                      All Applications ({applications?.length || 0})
                    </h3>
                    <p className="text-sm text-slate-600 mt-1">Manage and review candidate applications</p>
                  </div>
                </div>
              </div>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="border-slate-100">
                      <TableHead className="w-12">
                        <Checkbox />
                      </TableHead>
                      <TableHead className="text-slate-700 font-medium">Candidate</TableHead>
                      <TableHead className="text-slate-700 font-medium">Position</TableHead>
                      <TableHead className="text-slate-700 font-medium">Status</TableHead>
                      <TableHead className="text-slate-700 font-medium">Score</TableHead>
                      <TableHead className="text-slate-700 font-medium">Submitted</TableHead>
                      <TableHead className="text-right text-slate-700 font-medium">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8 text-slate-500">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                          <p>Loading applications...</p>
                        </TableCell>
                      </TableRow>
                    ) : applications && applications.length > 0 ? (
                      applications.map((app, index) => (
                        <TableRow key={app.id || index} className="hover:bg-slate-50 border-slate-100">
                          <TableCell>
                            <Checkbox />
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <div className="h-10 w-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                                <span className="text-sm font-medium text-white">
                                  {app.candidateName?.split(' ').map(n => n[0]).join('') || 'N/A'}
                                </span>
                              </div>
                              <div className="ml-4 min-w-0">
                                <div className="text-sm font-medium text-slate-900 truncate">{app.candidateName || 'Unknown'}</div>
                                <div className="text-xs text-slate-500 truncate">{app.candidateEmail || ''}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-sm text-slate-900">{app.jobTitle || 'Unknown Position'}</TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(app.status)}>
                              {app.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-sm text-slate-900">
                            {app.score ? `${app.score}/100` : 'N/A'}
                          </TableCell>
                          <TableCell className="text-sm text-slate-500">
                            {app.submittedAt ? formatDate(app.submittedAt) : 'N/A'}
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="hover:bg-slate-100">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => navigate(`/candidates/${app.candidateId}`)}>
                                  <Eye className="h-4 w-4 mr-2" />
                                  View Details
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit Status
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-red-600">
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8 text-slate-500">
                          <FileText className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                          <p>No applications found</p>
                          <p className="text-sm">Applications will appear here once submitted</p>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        )}

        {/* Forms Tab */}
        {activeTab === 'forms' && (
          <div className="space-y-6">
            <div className={`${shadows.card} p-6`}>
              <div className="text-center py-8">
                <Edit className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                <h3 className="text-lg font-semibold text-slate-900 mb-2">Form Management</h3>
                <p className="text-slate-600 mb-4">Create and manage application forms for your job postings</p>
                <Button onClick={() => navigate('/jobs')} className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Create New Form
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Performance Tab */}
        {activeTab === 'performance' && (
          <div className="space-y-6">
            <div className={`${shadows.card} p-6`}>
              <div className="text-center py-8">
                <BarChart3 className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                <h3 className="text-lg font-semibold text-slate-900 mb-2">Performance Analytics</h3>
                <p className="text-slate-600 mb-4">View detailed analytics and performance metrics</p>
                <Button onClick={() => navigate('/analytics')} className="bg-blue-600 hover:bg-blue-700">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  View Analytics
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ApplicationsTest;
