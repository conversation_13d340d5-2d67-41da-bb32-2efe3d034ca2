import express from 'express';
import { prisma } from '../index.js';
import { asyncHandler } from '../middleware/errorHandler.js';
import { CachedAnalyticsService } from '../services/CachedAnalyticsService.js';
import { unifiedDataService } from '../services/UnifiedDataService.js';
import { cacheManager } from '../cache/CacheManager.js';
import { AuthenticatedRequest } from '../middleware/auth.js';

const router = express.Router();
const analyticsService = new CachedAnalyticsService();

// Get unified dashboard analytics (candidate-centric)
router.get('/dashboard', asyncHandler(async (req: AuthenticatedRequest, res) => {
  // Use default company for testing when auth is disabled
  const companyId = req.user?.companyId || 'comp_1';

  try {
    console.log(`📊 Fetching unified dashboard data for company: ${companyId}`);

    // Use unified data service for candidate-centric metrics
    const unifiedData = await unifiedDataService.getUnifiedDashboardData(companyId);

    // Transform to match frontend expectations
    const dashboardResponse = {
      // Summary stats
      summary: {
        totalJobs: unifiedData.topJobs.length,
        totalApplications: unifiedData.totalApplications,
        totalCandidates: unifiedData.totalCandidates,
        totalInterviews: unifiedData.totalInterviews,
        activeJobs: unifiedData.topJobs.filter(j => j.applicationCount > 0).length,
        newApplicationsToday: unifiedData.newApplicationsThisMonth, // Simplified for now
      },

      // Status distribution (candidate-based)
      statusDistribution: unifiedData.candidatesByStatus,

      // Time series data
      applicationsByDate: unifiedData.timeSeriesData.map(d => ({
        date: d.date,
        count: d.applications,
      })),

      // Top jobs with candidate metrics
      topJobs: unifiedData.topJobs,

      // Recent applications (candidate-centric)
      recentApplications: unifiedData.recentActivity,

      // Time to hire metrics
      timeToHire: {
        averageDays: unifiedData.averageTimeToHire,
        totalHires: unifiedData.totalHires,
      },

      // Source data (candidate-centric)
      sources: unifiedData.topSources,

      // Change metrics (corrected growth formula)
      changeMetrics: {
        totalCandidates: {
          current: unifiedData.newCandidatesThisMonth,
          previous: Math.max(unifiedData.totalCandidates - unifiedData.newCandidatesThisMonth, 1),
          change: Math.round(((unifiedData.newCandidatesThisMonth - Math.max(unifiedData.totalCandidates - unifiedData.newCandidatesThisMonth, 1)) / Math.max(unifiedData.totalCandidates - unifiedData.newCandidatesThisMonth, 1)) * 100),
        },
        applications: {
          current: unifiedData.newApplicationsThisMonth,
          previous: Math.max(unifiedData.totalApplications - unifiedData.newApplicationsThisMonth, 1),
          change: Math.round(((unifiedData.newApplicationsThisMonth - Math.max(unifiedData.totalApplications - unifiedData.newApplicationsThisMonth, 1)) / Math.max(unifiedData.totalApplications - unifiedData.newApplicationsThisMonth, 1)) * 100),
        },
      },

      unified: true,
      candidateCentric: true,
      timestamp: new Date().toISOString(),
    };

    console.log(`✅ Unified dashboard data sent: ${unifiedData.totalCandidates} candidates, ${unifiedData.totalApplications} applications`);
    res.json(dashboardResponse);

  } catch (error) {
    console.error('❌ Unified dashboard analytics error:', error);
    res.status(500).json({
      error: 'Failed to fetch unified dashboard analytics',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}));

// Get recruitment timeline data
router.get('/recruitment', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const companyId = req.user?.companyId || 'comp_1';
  const { period = '30d' } = req.query;

  try {
    // Get applications over time for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const applications = await prisma.application.findMany({
      where: {
        job: { companyId },
        submittedAt: {
          not: null,
          gte: thirtyDaysAgo,
        },
      },
      select: {
        submittedAt: true,
        status: true,
      },
      orderBy: {
        submittedAt: 'asc',
      },
    });

    // Group by date and create timeline
    const timelineData = [];
    const dateMap = new Map();

    // Initialize all dates in the range
    for (let d = new Date(thirtyDaysAgo); d <= new Date(); d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split('T')[0];
      dateMap.set(dateStr, {
        date: dateStr,
        applications: 0,
        interviews: 0,
        offers: 0,
      });
    }

    // Count applications by date and status
    applications.forEach(app => {
      if (app.submittedAt) {
        const dateStr = app.submittedAt.toISOString().split('T')[0];
        const dayData = dateMap.get(dateStr);
        if (dayData) {
          dayData.applications++;
          if (['interview', 'assessment'].includes(app.status)) {
            dayData.interviews++;
          }
          if (['offer', 'hired'].includes(app.status)) {
            dayData.offers++;
          }
        }
      }
    });

    const data = Array.from(dateMap.values());

    res.json({
      data,
      period,
      totalApplications: applications.length,
    });
  } catch (error) {
    console.error('Recruitment data error:', error);
    res.status(500).json({ error: 'Failed to fetch recruitment data' });
  }
}));

// Cache management endpoints
router.post('/cache/warm', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const companyId = req.user?.companyId || 'comp_1';

  try {
    await analyticsService.warmCompanyCache(companyId);
    res.json({
      message: 'Cache warmed successfully',
      companyId,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Cache warming error:', error);
    res.status(500).json({ error: 'Failed to warm cache' });
  }
}));

router.post('/cache/refresh', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const companyId = req.user?.companyId || 'comp_1';

  try {
    await analyticsService.refreshDashboardCache(companyId);
    res.json({
      message: 'Cache refreshed successfully',
      companyId,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Cache refresh error:', error);
    res.status(500).json({ error: 'Failed to refresh cache' });
  }
}));

router.get('/cache/stats', asyncHandler(async (req: AuthenticatedRequest, res) => {
  try {
    const stats = await analyticsService.getCacheStats();
    res.json({
      stats,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Cache stats error:', error);
    res.status(500).json({ error: 'Failed to get cache stats' });
  }
}));

// Get hiring funnel analytics
router.get('/funnel', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const companyId = req.user?.companyId || 'comp_1';
  const { jobId, startDate, endDate } = req.query;

  const where: any = {
    job: { companyId },
  };

  if (jobId) {
    where.jobId = jobId as string;
  }

  if (startDate && endDate) {
    where.submittedAt = {
      gte: new Date(startDate as string),
      lte: new Date(endDate as string),
    };
  }

  // Get funnel data
  const funnelStages = ['applied', 'screening', 'interview', 'assessment', 'offer', 'hired'];
  
  const funnelData = await Promise.all(
    funnelStages.map(async (stage) => {
      const count = await prisma.application.count({
        where: {
          ...where,
          status: stage,
        },
      });
      return { stage, count };
    })
  );

  // Calculate conversion rates
  const totalApplications = funnelData[0].count;
  const funnelWithRates = funnelData.map((stage, index) => ({
    ...stage,
    conversionRate: totalApplications > 0 ? (stage.count / totalApplications) * 100 : 0,
    dropoffRate: index > 0 ? 
      ((funnelData[index - 1].count - stage.count) / funnelData[index - 1].count) * 100 : 0,
  }));

  res.json({
    funnel: funnelWithRates,
    totalApplications,
  });
}));

// Get time-to-hire analytics
router.get('/time-to-hire', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const companyId = req.user?.companyId || 'comp_1';

  // Get hired applications with their timeline
  const hiredApplications = await prisma.application.findMany({
    where: {
      job: { companyId },
      status: 'hired',
      submittedAt: { not: null },
    },
    include: {
      job: {
        select: {
          title: true,
          department: true,
        },
      },
      candidate: {
        select: {
          firstName: true,
          lastName: true,
        },
      },
    },
  });

  // Calculate time to hire for each application
  const timeToHireData = hiredApplications
    .filter(app => app.submittedAt && app.hiredAt)
    .map(app => {
      const submittedDate = new Date(app.submittedAt!);
      const hiredDate = new Date(app.hiredAt!);
      const daysToHire = Math.ceil((hiredDate.getTime() - submittedDate.getTime()) / (1000 * 60 * 60 * 24));

      return {
        applicationId: app.id,
        candidate: `${app.candidate.firstName} ${app.candidate.lastName}`,
        jobTitle: app.job.title,
        department: app.job.department,
        daysToHire,
        submittedAt: app.submittedAt,
        hiredAt: app.hiredAt,
      };
    });

  // Calculate averages
  const averageTimeToHire = timeToHireData.length > 0 
    ? timeToHireData.reduce((sum, item) => sum + item.daysToHire, 0) / timeToHireData.length 
    : 0;

  // Group by department
  const byDepartment = timeToHireData.reduce((acc, item) => {
    const dept = item.department || 'Unknown';
    if (!acc[dept]) {
      acc[dept] = [];
    }
    acc[dept].push(item.daysToHire);
    return acc;
  }, {} as Record<string, number[]>);

  const departmentAverages = Object.entries(byDepartment).map(([department, days]) => ({
    department,
    averageDays: days.reduce((sum, day) => sum + day, 0) / days.length,
    hires: days.length,
  }));

  res.json({
    averageTimeToHire: Math.round(averageTimeToHire),
    totalHires: timeToHireData.length,
    timeToHireData,
    departmentAverages,
  });
}));

// Get source effectiveness analytics
router.get('/sources', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const companyId = req.user?.companyId || 'comp_1';

  try {
    // Get applications with source data (try new schema first, fallback to metadata)
    const applications = await prisma.application.findMany({
      where: {
        job: { companyId },
      },
      select: {
        status: true,
        metadata: true,
        source: {
          select: {
            id: true,
            name: true,
            category: true,
            cost: true,
          },
        },
      },
    });

    // Extract source data and calculate metrics
    const sourceData = applications.reduce((acc, app) => {
      // Try to get source from new schema first, then fallback to metadata
      let sourceName = app.source?.name;
      if (!sourceName) {
        const metadata = app.metadata as any;
        sourceName = metadata?.source || 'Unknown';
      }

      if (!acc[sourceName]) {
        acc[sourceName] = {
          total: 0,
          hired: 0,
          interviewed: 0,
          category: app.source?.category || 'unknown',
          cost: app.source?.cost || 0,
        };
      }

      acc[sourceName].total++;

      if (app.status === 'hired') {
        acc[sourceName].hired++;
      }

      if (['interview', 'assessment', 'offer', 'hired'].includes(app.status)) {
        acc[sourceName].interviewed++;
      }

      return acc;
    }, {} as Record<string, { total: number; hired: number; interviewed: number; category: string; cost: number }>);

    // Calculate conversion rates
    const sourceEffectiveness = Object.entries(sourceData).map(([source, data]) => ({
      source,
      applications: data.total,
      interviews: data.interviewed,
      hires: data.hired,
      category: data.category,
      cost: data.cost,
      interviewRate: data.total > 0 ? (data.interviewed / data.total) * 100 : 0,
      hireRate: data.total > 0 ? (data.hired / data.total) * 100 : 0,
      costPerApplication: data.cost,
      costPerHire: data.hired > 0 ? (data.cost * data.total) / data.hired : 0,
    }));

    res.json({
      sourceEffectiveness: sourceEffectiveness.sort((a, b) => b.applications - a.applications),
      totalApplications: applications.length,
    });
  } catch (error) {
    console.error('Source analytics error:', error);

    // Fallback to simple metadata-based approach if new schema fails
    const applications = await prisma.application.findMany({
      where: {
        job: { companyId },
      },
      select: {
        status: true,
        metadata: true,
      },
    });

    const sourceData = applications.reduce((acc, app) => {
      const metadata = app.metadata as any;
      const source = metadata?.source || 'Unknown';

      if (!acc[source]) {
        acc[source] = { total: 0, hired: 0, interviewed: 0 };
      }

      acc[source].total++;
      if (app.status === 'hired') acc[source].hired++;
      if (['interview', 'assessment', 'offer', 'hired'].includes(app.status)) acc[source].interviewed++;

      return acc;
    }, {} as Record<string, { total: number; hired: number; interviewed: number }>);

    const sourceEffectiveness = Object.entries(sourceData).map(([source, data]) => ({
      source,
      applications: data.total,
      interviews: data.interviewed,
      hires: data.hired,
      interviewRate: data.total > 0 ? (data.interviewed / data.total) * 100 : 0,
      hireRate: data.total > 0 ? (data.hired / data.total) * 100 : 0,
    }));

    res.json({
      sourceEffectiveness: sourceEffectiveness.sort((a, b) => b.applications - a.applications),
      totalApplications: applications.length,
    });
  }
}));

// Get change metrics (percentage changes)
router.get('/changes', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const companyId = req.user?.companyId || 'comp_1';

  try {
    const changeMetrics = await analyticsService.getChangeMetrics(companyId);
    res.json(changeMetrics);
  } catch (error) {
    console.error('Change metrics error:', error);
    res.status(500).json({ error: 'Failed to fetch change metrics' });
  }
}));

// Get top jobs with enhanced data
router.get('/top-jobs', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const companyId = req.user?.companyId || 'comp_1';
  const limit = parseInt(req.query.limit as string) || 5;

  try {
    const topJobs = await analyticsService.getTopJobs(companyId, limit);
    res.json({ jobs: topJobs });
  } catch (error) {
    console.error('Top jobs analytics error:', error);
    res.status(500).json({ error: 'Failed to fetch top jobs' });
  }
}));

// GET /api/analytics/performance - Performance metrics for Application Management
router.get('/performance', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const companyId = req.user?.companyId || 'comp_1';
  const { timeframe = '30d' } = req.query;

  try {
    // Calculate date range based on timeframe
    const now = new Date();
    let startDate = new Date();

    switch (timeframe) {
      case '7d':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(now.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(now.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate.setDate(now.getDate() - 30);
    }

    const whereClause = { job: { companyId } };
    const timeFilteredWhere = {
      ...whereClause,
      submittedAt: { gte: startDate }
    };

    // Get performance metrics
    const [
      totalApplications,
      applicationsByStatus,
      applicationsByDate,
      formPerformance,
      conversionFunnel
    ] = await Promise.all([
      // Total applications in timeframe
      prisma.application.count({ where: timeFilteredWhere }),

      // Applications by status
      prisma.application.groupBy({
        by: ['status'],
        where: timeFilteredWhere,
        _count: true,
      }),

      // Applications by date for trend analysis
      prisma.application.findMany({
        where: timeFilteredWhere,
        select: {
          submittedAt: true,
          status: true,
        },
        orderBy: { submittedAt: 'asc' },
      }),

      // Form performance metrics
      prisma.applicationFormSchema.findMany({
        where: { job: { companyId } },
        include: {
          job: {
            select: {
              title: true,
              _count: {
                select: {
                  applications: true,
                },
              },
            },
          },
        },
      }),

      // Conversion funnel data
      prisma.application.groupBy({
        by: ['status'],
        where: whereClause,
        _count: true,
      })
    ]);

    // Process applications by date for trend chart
    const dateMap = new Map();
    const daysDiff = Math.ceil((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    // Initialize all dates
    for (let i = 0; i < daysDiff; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      const dateStr = date.toISOString().split('T')[0];
      dateMap.set(dateStr, { date: dateStr, applications: 0, interviews: 0, offers: 0 });
    }

    // Count applications by date
    applicationsByDate.forEach(app => {
      if (app.submittedAt) {
        const dateStr = app.submittedAt.toISOString().split('T')[0];
        const dayData = dateMap.get(dateStr);
        if (dayData) {
          dayData.applications++;
          if (['interview', 'assessment'].includes(app.status)) {
            dayData.interviews++;
          }
          if (['offer', 'hired'].includes(app.status)) {
            dayData.offers++;
          }
        }
      }
    });

    const trendData = Array.from(dateMap.values());

    // Calculate conversion rates
    const totalApps = conversionFunnel.reduce((sum, item) => sum + item._count, 0);
    const funnelData = [
      { stage: 'Applied', count: conversionFunnel.find(s => s.status === 'applied')?._count || 0 },
      { stage: 'Screening', count: conversionFunnel.find(s => s.status === 'screening')?._count || 0 },
      { stage: 'Interview', count: conversionFunnel.find(s => s.status === 'interview')?._count || 0 },
      { stage: 'Assessment', count: conversionFunnel.find(s => s.status === 'assessment')?._count || 0 },
      { stage: 'Offer', count: conversionFunnel.find(s => s.status === 'offer')?._count || 0 },
      { stage: 'Hired', count: conversionFunnel.find(s => s.status === 'hired')?._count || 0 },
    ].map(stage => ({
      ...stage,
      percentage: totalApps > 0 ? parseFloat(((stage.count / totalApps) * 100).toFixed(1)) : 0
    }));

    // Form performance with mock view data
    const formMetrics = formPerformance.map(form => ({
      id: form.id,
      title: form.title,
      jobTitle: form.job.title,
      submissions: form.job._count.applications,
      views: Math.floor(Math.random() * 500) + 100, // Mock data
      conversionRate: form.job._count.applications > 0 ?
        parseFloat(((form.job._count.applications / (Math.floor(Math.random() * 500) + 100)) * 100).toFixed(1)) : 0,
      status: 'live' // Simplified
    }));

    res.json({
      timeframe,
      totalApplications,
      applicationsByStatus: applicationsByStatus.map(status => ({
        status: status.status,
        count: status._count,
        percentage: totalApplications > 0 ?
          parseFloat(((status._count / totalApplications) * 100).toFixed(1)) : 0
      })),
      trendData,
      conversionFunnel: funnelData,
      formPerformance: formMetrics,
      averageTimeToHire: 14, // Mock data - would calculate from actual hire dates
      topPerformingForms: formMetrics
        .sort((a, b) => b.conversionRate - a.conversionRate)
        .slice(0, 5),
      generatedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Performance analytics error:', error);
    res.status(500).json({
      error: 'Failed to fetch performance analytics',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}));

// GET /api/analytics/application-trends - Application trend analysis
router.get('/application-trends', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const companyId = req.user?.companyId || 'comp_1';
  const { period = '30d', granularity = 'daily' } = req.query;

  try {
    let startDate = new Date();
    switch (period) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
    }

    const applications = await prisma.application.findMany({
      where: {
        job: { companyId },
        submittedAt: { gte: startDate }
      },
      select: {
        submittedAt: true,
        status: true,
        source: true,
        job: {
          select: {
            title: true,
            department: true,
          },
        },
      },
      orderBy: { submittedAt: 'asc' },
    });

    // Group by time period
    const groupedData = applications.reduce((acc, app) => {
      if (!app.submittedAt) return acc;

      const date = new Date(app.submittedAt);
      let key: string;

      if (granularity === 'daily') {
        key = date.toISOString().split('T')[0];
      } else if (granularity === 'weekly') {
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        key = weekStart.toISOString().split('T')[0];
      } else { // monthly
        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      }

      if (!acc[key]) {
        acc[key] = {
          period: key,
          total: 0,
          byStatus: {},
          bySource: {},
          byDepartment: {},
        };
      }

      acc[key].total++;
      acc[key].byStatus[app.status] = (acc[key].byStatus[app.status] || 0) + 1;
      acc[key].bySource[app.source || 'Direct'] = (acc[key].bySource[app.source || 'Direct'] || 0) + 1;
      acc[key].byDepartment[app.job.department || 'Unknown'] = (acc[key].byDepartment[app.job.department || 'Unknown'] || 0) + 1;

      return acc;
    }, {} as Record<string, any>);

    const trendData = Object.values(groupedData).sort((a: any, b: any) =>
      a.period.localeCompare(b.period)
    );

    res.json({
      period,
      granularity,
      data: trendData,
      summary: {
        totalApplications: applications.length,
        averagePerPeriod: trendData.length > 0 ?
          Math.round(applications.length / trendData.length) : 0,
        peakPeriod: trendData.reduce((max: any, current: any) =>
          current.total > (max?.total || 0) ? current : max, null),
      },
    });

  } catch (error) {
    console.error('Application trends error:', error);
    res.status(500).json({ error: 'Failed to fetch application trends' });
  }
}));

export default router;
