# TalentSol ATS - Multi-API Architecture Docker Compose
version: '3.8'

services:
  # API Gateway (Nginx)
  api-gateway:
    image: nginx:alpine
    container_name: talentsol-api-gateway
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./api-gateway/nginx.conf:/etc/nginx/nginx.conf
      - ./api-gateway/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/ssl
    depends_on:
      - rest-api
      - graphql-api
      - postgres-api
      - ai-api
      - mdx-api
      - dax-api
    networks:
      - talentsol-network
    restart: unless-stopped

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: talentsol-postgres
    environment:
      POSTGRES_DB: talentsol_ats
      POSTGRES_USER: talentsol_user
      POSTGRES_PASSWORD: talentsol_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - talentsol-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: talentsol-redis
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - talentsol-network
    restart: unless-stopped

  # REST API (Current Node.js/Express API)
  rest-api:
    build:
      context: .
      dockerfile: Dockerfile.rest
    container_name: talentsol-rest-api
    environment:
      NODE_ENV: production
      DATABASE_URL: ************************************************************/talentsol_ats
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: ${JWT_SECRET}
      PORT: 3000
    volumes:
      - ./uploads:/app/uploads
    ports:
      - "3000:3000"
    depends_on:
      - postgres
      - redis
    networks:
      - talentsol-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # GraphQL API
  graphql-api:
    build:
      context: ./apis
      dockerfile: Dockerfile.graphql
    container_name: talentsol-graphql-api
    environment:
      NODE_ENV: production
      DATABASE_URL: ************************************************************/talentsol_ats
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: ${JWT_SECRET}
      PORT: 4000
    ports:
      - "4000:4000"
    depends_on:
      - postgres
      - redis
    networks:
      - talentsol-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL-Compatible SQL API
  postgres-api:
    build:
      context: ./apis
      dockerfile: Dockerfile.sql
    container_name: talentsol-postgres-api
    environment:
      DATABASE_URL: ************************************************************/talentsol_ats
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: ${JWT_SECRET}
      PORT: 5000
    ports:
      - "5000:5000"
    depends_on:
      - postgres
      - redis
    networks:
      - talentsol-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # AI/ML API
  ai-api:
    build:
      context: ./apis
      dockerfile: Dockerfile.ai
    container_name: talentsol-ai-api
    environment:
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: ${JWT_SECRET}
      PORT: 8000
      MODEL_PATH: /app/models
    volumes:
      - ./models:/app/models
      - ai_cache:/app/cache
    ports:
      - "8000:8000"
    depends_on:
      - redis
    networks:
      - talentsol-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MDX API (Documentation)
  mdx-api:
    build:
      context: ./apis
      dockerfile: Dockerfile.mdx
    container_name: talentsol-mdx-api
    environment:
      NODE_ENV: production
      PORT: 7000
    volumes:
      - ./docs:/app/docs
    ports:
      - "7000:7000"
    networks:
      - talentsol-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # DAX API (Analytics)
  dax-api:
    build:
      context: ./apis
      dockerfile: Dockerfile.dax
    container_name: talentsol-dax-api
    environment:
      DATABASE_URL: ************************************************************/talentsol_ats
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: ${JWT_SECRET}
      PORT: 6000
    ports:
      - "6000:6000"
    depends_on:
      - postgres
      - redis
    networks:
      - talentsol-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # WebSocket API (Real-time features)
  websocket-api:
    build:
      context: ./apis
      dockerfile: Dockerfile.websocket
    container_name: talentsol-websocket-api
    environment:
      NODE_ENV: production
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: ${JWT_SECRET}
      PORT: 9000
    ports:
      - "9000:9000"
    depends_on:
      - redis
    networks:
      - talentsol-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring and Observability
  prometheus:
    image: prom/prometheus:latest
    container_name: talentsol-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - talentsol-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: talentsol-grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3001:3000"
    depends_on:
      - prometheus
    networks:
      - talentsol-network
    restart: unless-stopped

  # Log aggregation
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: talentsol-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - talentsol-network
    restart: unless-stopped

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: talentsol-kibana
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - talentsol-network
    restart: unless-stopped

  # Message Queue (for async processing)
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: talentsol-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: talentsol
      RABBITMQ_DEFAULT_PASS: talentsol_password
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    networks:
      - talentsol-network
    restart: unless-stopped

networks:
  talentsol-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  ai_cache:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local
  rabbitmq_data:
    driver: local
